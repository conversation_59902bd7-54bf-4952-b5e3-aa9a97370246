using Microsoft.ML;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using System.Text.Json;

namespace SmaTrendFollower.MachineLearning.Prediction;

/// <summary>
/// Interface for ML-based signal ranking and scoring
/// </summary>
public interface ISignalRanker
{
    /// <summary>
    /// Scores a signal using the trained ML model
    /// </summary>
    float Score(SignalFeatures features);

    /// <summary>
    /// Scores multiple signals and returns ranked results
    /// </summary>
    IEnumerable<(TradingSignal Signal, float Score, float Probability)> ScoreAndRank(
        IEnumerable<TradingSignal> signals, 
        Func<TradingSignal, SignalFeatures> featureExtractor);

    /// <summary>
    /// Filters signals based on ML probability threshold
    /// </summary>
    IEnumerable<TradingSignal> FilterByProbability(
        IEnumerable<TradingSignal> signals,
        Func<TradingSignal, SignalFeatures> featureExtractor,
        float minProbability = 0.65f);

    /// <summary>
    /// Gets model information and metadata
    /// </summary>
    MLModelInfo? GetModelInfo();

    /// <summary>
    /// Checks if the model is loaded and ready
    /// </summary>
    bool IsModelLoaded { get; }
}

/// <summary>
/// ML-based signal ranking service using trained binary classification model.
/// Provides real-time scoring and filtering of trading signals.
/// </summary>
public sealed class SignalRanker : ISignalRanker, IDisposable
{
    private readonly MLContext _mlContext;
    private readonly ILogger<SignalRanker> _logger;
    private readonly string _modelPath;
    private PredictionEngine<MLSignalInput, MLSignalOutput>? _predictionEngine;
    private MLModelInfo? _modelInfo;
    private bool _disposed;

    public bool IsModelLoaded => _predictionEngine != null;

    public SignalRanker(MLContext mlContext, IConfiguration configuration, ILogger<SignalRanker> logger)
    {
        _mlContext = mlContext;
        _logger = logger;
        _modelPath = configuration.GetValue<string>("ML:ModelPath") ?? "Model/signal_model.zip";
        
        // Try to load model on initialization
        _ = Task.Run(LoadModelAsync);
    }

    /// <summary>
    /// Scores a signal using the ML model
    /// </summary>
    public float Score(SignalFeatures features)
    {
        if (_predictionEngine == null)
        {
            _logger.LogWarning("ML model not loaded, returning default score");
            return 0.5f; // Default neutral score
        }

        try
        {
            var input = new MLSignalInput
            {
                SmaGap = features.SmaGap,
                Volatility = features.Volatility,
                Rsi = features.Rsi,
                BreadthScore = features.BreadthScore,
                VixLevel = features.VixLevel,
                SixMonthReturn = features.SixMonthReturn,
                RelativeVolume = features.RelativeVolume,
                MarketRegime = features.MarketRegime
            };

            var prediction = _predictionEngine.Predict(input);
            return prediction.Probability;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scoring signal with ML model");
            return 0.5f; // Default neutral score on error
        }
    }

    /// <summary>
    /// Scores multiple signals and returns ranked results
    /// </summary>
    public IEnumerable<(TradingSignal Signal, float Score, float Probability)> ScoreAndRank(
        IEnumerable<TradingSignal> signals, 
        Func<TradingSignal, SignalFeatures> featureExtractor)
    {
        if (_predictionEngine == null)
        {
            _logger.LogWarning("ML model not loaded, returning signals with default scores");
            return signals.Select(s => (s, 0.5f, 0.5f));
        }

        var scoredSignals = new List<(TradingSignal Signal, float Score, float Probability)>();

        foreach (var signal in signals)
        {
            try
            {
                var features = featureExtractor(signal);
                var probability = Score(features);
                var score = CalculateCompositeScore(signal, features, probability);
                
                scoredSignals.Add((signal, score, probability));
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error scoring signal for {Symbol}", signal.Symbol);
                scoredSignals.Add((signal, 0.5f, 0.5f)); // Default score on error
            }
        }

        // Return ranked by composite score (descending)
        return scoredSignals.OrderByDescending(x => x.Score);
    }

    /// <summary>
    /// Filters signals based on ML probability threshold
    /// </summary>
    public IEnumerable<TradingSignal> FilterByProbability(
        IEnumerable<TradingSignal> signals,
        Func<TradingSignal, SignalFeatures> featureExtractor,
        float minProbability = 0.65f)
    {
        if (_predictionEngine == null)
        {
            _logger.LogWarning("ML model not loaded, returning all signals");
            return signals;
        }

        var filteredSignals = new List<TradingSignal>();
        var totalSignals = 0;
        var passedSignals = 0;

        foreach (var signal in signals)
        {
            totalSignals++;
            
            try
            {
                var features = featureExtractor(signal);
                var probability = Score(features);
                
                if (probability >= minProbability)
                {
                    filteredSignals.Add(signal);
                    passedSignals++;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error filtering signal for {Symbol}", signal.Symbol);
                // Include signal on error to be conservative
                filteredSignals.Add(signal);
                passedSignals++;
            }
        }

        _logger.LogInformation("ML filter: {PassedSignals}/{TotalSignals} signals passed (threshold: {Threshold:P1})",
            passedSignals, totalSignals, minProbability);

        return filteredSignals;
    }

    /// <summary>
    /// Gets model information and metadata
    /// </summary>
    public MLModelInfo? GetModelInfo()
    {
        return _modelInfo;
    }

    /// <summary>
    /// Loads the ML model from disk
    /// </summary>
    private async Task LoadModelAsync()
    {
        try
        {
            if (!File.Exists(_modelPath))
            {
                _logger.LogWarning("ML model file not found: {ModelPath}", _modelPath);
                return;
            }

            _logger.LogInformation("Loading ML model from {ModelPath}...", _modelPath);

            // Load the model
            var model = _mlContext.Model.Load(_modelPath, out var modelInputSchema);
            _predictionEngine = _mlContext.Model.CreatePredictionEngine<MLSignalInput, MLSignalOutput>(model);

            // Load metadata if available
            var metadataPath = Path.ChangeExtension(_modelPath, ".metadata.json");
            if (File.Exists(metadataPath))
            {
                var metadataJson = await File.ReadAllTextAsync(metadataPath);
                var metadata = JsonSerializer.Deserialize<JsonElement>(metadataJson);
                
                _modelInfo = new MLModelInfo(
                    _modelPath,
                    metadata.GetProperty("ModelVersion").GetString() ?? "Unknown",
                    metadata.GetProperty("TrainedAt").GetDateTime(),
                    (float)metadata.GetProperty("Metrics").GetProperty("Accuracy").GetDouble(),
                    (float)metadata.GetProperty("Metrics").GetProperty("Precision").GetDouble(),
                    (float)metadata.GetProperty("Metrics").GetProperty("Recall").GetDouble(),
                    (float)metadata.GetProperty("Metrics").GetProperty("F1Score").GetDouble(),
                    metadata.GetProperty("TrainingSamples").GetInt32(),
                    metadata.GetProperty("ValidationSamples").GetInt32()
                );
            }
            else
            {
                _modelInfo = new MLModelInfo(
                    _modelPath,
                    "Unknown",
                    File.GetLastWriteTime(_modelPath),
                    0.0f, 0.0f, 0.0f, 0.0f, 0, 0
                );
            }

            _logger.LogInformation("✅ ML model loaded successfully");
            if (_modelInfo != null)
            {
                _logger.LogInformation("Model info: Version={Version}, Accuracy={Accuracy:P2}, TrainedAt={TrainedAt}",
                    _modelInfo.ModelVersion, _modelInfo.Accuracy, _modelInfo.TrainedAt);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load ML model from {ModelPath}", _modelPath);
        }
    }

    /// <summary>
    /// Calculates a composite score combining ML probability with other factors
    /// </summary>
    private static float CalculateCompositeScore(TradingSignal signal, SignalFeatures features, float mlProbability)
    {
        // Combine ML probability with traditional factors
        var momentumScore = Math.Min(1.0f, Math.Max(0.0f, features.SixMonthReturn * 2.0f)); // Scale 6-month return
        var volatilityScore = 1.0f - Math.Min(1.0f, features.Volatility * 10.0f); // Lower volatility = higher score
        var rsiScore = features.Rsi switch
        {
            < 30 => 0.8f, // Oversold - good for mean reversion
            > 70 => 0.3f, // Overbought - lower score
            _ => 0.6f     // Neutral
        };

        // Weighted combination
        var compositeScore = 
            mlProbability * 0.5f +           // 50% ML probability
            momentumScore * 0.25f +          // 25% momentum
            volatilityScore * 0.15f +        // 15% volatility (inverse)
            rsiScore * 0.10f;                // 10% RSI positioning

        return Math.Min(1.0f, Math.Max(0.0f, compositeScore));
    }

    /// <summary>
    /// Reloads the model from disk (useful for hot-swapping updated models)
    /// </summary>
    public async Task ReloadModelAsync()
    {
        _logger.LogInformation("Reloading ML model...");
        
        // Dispose current prediction engine
        _predictionEngine?.Dispose();
        _predictionEngine = null;
        _modelInfo = null;

        // Reload model
        await LoadModelAsync();
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _predictionEngine?.Dispose();
            _disposed = true;
        }
    }
}
