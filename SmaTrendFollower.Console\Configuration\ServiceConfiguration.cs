using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Serilog;
using SmaTrendFollower.Services;
using SmaTrendFollower.Data;
using SmaTrendFollower.Examples;
using SmaTrendFollower.Infrastructure;
using SmaTrendFollower.Console.Configuration;
using Quartz;

namespace SmaTrendFollower.Configuration;

/// <summary>
/// Centralized service configuration to eliminate duplicate registrations and ensure consistency
/// </summary>
public static class ServiceConfiguration
{
    /// <summary>
    /// Configure HTTP clients with Polly resilience policies
    /// - Retry policy: 3 attempts with exponential back-off (2s, 4s, 8s)
    /// - Circuit breaker: Opens after 5 failures for 30 seconds
    /// - Handles 5xx errors, network failures, and 429 rate limiting
    /// </summary>
    public static IServiceCollection AddPollyHttpClients(this IServiceCollection services, IConfiguration cfg)
    {
        var loggerFactory = LoggerFactory.Create(builder => builder.AddSerilog());
        var logger = loggerFactory.CreateLogger("PollyHttpClients");

        // Configure timeouts from configuration
        var timeouts = cfg.ConfigureTimeouts();
        timeouts.ValidateTimeouts(logger);

        // Configure Polygon HTTP client with Polly policies
        services.AddHttpClient("Polygon")
            .ConfigureHttpClient(c =>
            {
                c.BaseAddress = new Uri("https://api.polygon.io");
                c.Timeout = timeouts.Http.StandardRequest;
                c.DefaultRequestHeaders.Add("User-Agent", "SmaTrendFollower/1.0");
                c.DefaultRequestHeaders.Add("Accept", "application/json");
            })
            .AddPolicyHandler(PollyPolicies.GetRetryPolicyWithLogging(logger, "Polygon"))
            .AddPolicyHandler(PollyPolicies.GetCircuitBreakerPolicyWithLogging(logger, "Polygon"));

        // Configure Alpaca HTTP client with Polly policies
        services.AddHttpClient("Alpaca")
            .ConfigureHttpClient(c =>
            {
                c.BaseAddress = new Uri("https://api.alpaca.markets");
                c.Timeout = timeouts.Http.StandardRequest;
                c.DefaultRequestHeaders.Add("User-Agent", "SmaTrendFollower/1.0");
                c.DefaultRequestHeaders.Add("Accept", "application/json");
            })
            .AddPolicyHandler(PollyPolicies.GetRetryPolicyWithLogging(logger, "Alpaca"))
            .AddPolicyHandler(PollyPolicies.GetCircuitBreakerPolicyWithLogging(logger, "Alpaca"));

        logger.LogInformation("✅ Polly HTTP clients configured with resilience policies");
        return services;
    }

    /// <summary>
    /// Register core infrastructure services (factories, time providers)
    /// Note: HTTP clients are now configured by AddPollyHttpClients()
    /// </summary>
    public static IServiceCollection AddCoreInfrastructure(this IServiceCollection services)
    {
        // HTTP client base configuration (AddPollyHttpClients should be called first)
        services.AddHttpClient();

        // Configure connection pooling settings
        HttpClientConfigurationService.ConfigureConnectionPooling();

        // Rate limiting and client factories
        services.AddSingleton<IRateLimitPolicyFactory, RateLimitPolicyFactory>();
        services.AddSingleton<IAlpacaClientFactory, AlpacaClientFactory>();
        services.AddSingleton<IPolygonClientFactory, PolygonClientFactory>();
        services.AddSingleton<IPolygonWebSocketClient, PolygonWebSocketClient>();

        // Register advanced Polygon WebSocket manager for batch subscriptions and rate limiting
        services.AddSingleton<PolygonWebSocketManager>(provider =>
        {
            var redisService = provider.GetRequiredService<IOptimizedRedisConnectionService>();
            var logger = provider.GetRequiredService<ILogger<PolygonWebSocketManager>>();
            var configuration = provider.GetRequiredService<IConfiguration>();
            var metricsService = provider.GetService<ITradingMetricsService>(); // Optional dependency
            var apiKey = configuration["POLY_API_KEY"] ?? throw new InvalidOperationException("POLY_API_KEY not configured");

            return new PolygonWebSocketManager(redisService, logger, apiKey, metricsService);
        });
        
        // Time and session management
        services.AddSingleton<ITimeProvider, SystemTimeProvider>();
        services.AddSingleton<IMarketSessionGuard, MarketSessionGuard>();

        // Trading cycle management
        services.AddSingleton<ITradingCycleManager>(provider =>
        {
            var marketDataService = provider.GetRequiredService<IMarketDataService>();
            var marketSessionGuard = provider.GetRequiredService<IMarketSessionGuard>();
            var logger = provider.GetRequiredService<ILogger<TradingCycleManager>>();
            var configuration = provider.GetRequiredService<Microsoft.Extensions.Configuration.IConfiguration>();
            var config = TradingCycleConfig.FromConfiguration(configuration);
            return new TradingCycleManager(marketDataService, marketSessionGuard, logger, config);
        });

        // API health monitoring
        services.AddSingleton<IApiHealthMonitor, ApiHealthMonitor>();

        return services;
    }

    /// <summary>
    /// Register database contexts and caching services
    /// </summary>
    public static IServiceCollection AddDataServices(this IServiceCollection services)
    {
        // Database contexts - FIXED: Use DbContextFactory for thread-safe access
        services.AddDbContextFactory<IndexCacheDbContext>(options =>
            options.UseSqlite("Data Source=index_cache.db"));
        services.AddDbContextFactory<StockBarCacheDbContext>(options =>
            options.UseSqlite("Data Source=stock_cache.db"));

        // Cache services - FIXED: Use factory pattern for thread safety
        services.AddScoped<IIndexCacheService, SmaTrendFollower.Console.Services.ThreadSafeIndexCacheService>();
        services.AddScoped<IStockBarCacheService, SmaTrendFollower.Console.Services.ThreadSafeStockBarCacheService>();
        services.AddScoped<ICacheMaintenanceService, CacheMaintenanceService>();

        // Database initialization service
        services.AddScoped<IDatabaseInitializationService, DatabaseInitializationService>();

        // Optimized performance services
        services.AddScoped<IOptimizedBulkOperationsService, OptimizedBulkOperationsService>();
        services.AddScoped<IAdvancedCacheOptimizationService, AdvancedCacheOptimizationService>();

        // Redis and state management
        services.AddSingleton<IOptimizedRedisConnectionService, OptimizedRedisConnectionService>();

        // Register raw ConnectionMultiplexer for services that need it directly
        services.AddSingleton<StackExchange.Redis.IConnectionMultiplexer>(provider =>
        {
            var configuration = provider.GetRequiredService<IConfiguration>();
            var connectionString = configuration.GetConnectionString("Redis") ?? "*************:6379";
            return StackExchange.Redis.ConnectionMultiplexer.Connect(connectionString);
        });

        // Also register the concrete type for services that need it
        services.AddSingleton<StackExchange.Redis.ConnectionMultiplexer>(provider =>
        {
            var connectionMultiplexer = provider.GetRequiredService<StackExchange.Redis.IConnectionMultiplexer>();
            return (StackExchange.Redis.ConnectionMultiplexer)connectionMultiplexer;
        });

        services.AddSingleton<ILiveStateStore, LiveStateStore>();
        services.AddScoped<IBarStore, HistoricalBarStore>();
        services.AddScoped<IRedisWarmingService, RedisWarmingService>();

        return services;
    }

    /// <summary>
    /// Register market data and universe services
    /// </summary>
    public static IServiceCollection AddMarketDataServices(this IServiceCollection services)
    {
        // Market data services
        services.AddSingleton<IVixFallbackService, VixFallbackService>();
        services.AddSingleton<ISyntheticVixService, SyntheticVixService>();
        services.AddSingleton<SyntheticVixTrainer>(); // Quartz will create job instances
        services.AddSingleton<IMarketDataService, MarketDataService>();
        services.AddSingleton<IStreamingDataService, StreamingDataService>();

        // Universe providers
        services.AddSingleton<IUniverseProvider, HybridUniverseProvider>();
        services.AddScoped<IDynamicUniverseProvider, DynamicUniverseProvider>();
        services.AddScoped<IUniverseFetcherService, UniverseFetcherService>();

        return services;
    }

    /// <summary>
    /// Register safety and risk management services
    /// </summary>
    public static IServiceCollection AddSafetyServices(this IServiceCollection services)
    {
        services.AddSingleton<ISafetyConfigurationService, SafetyConfigurationService>();
        services.AddScoped<IDynamicSafetyConfigurationService, DynamicSafetyConfigurationService>();
        services.AddSingleton<ITradingSafetyGuard, TradingSafetyGuard>();

        return services;
    }

    /// <summary>
    /// Register core trading strategy services
    /// </summary>
    public static IServiceCollection AddTradingServices(this IServiceCollection services)
    {
        // Signal generation and filtering
        services.AddScoped<IMomentumFilter, MomentumFilter>();
        services.AddScoped<IVolatilityFilter, VolatilityFilter>();
        services.AddScoped<IPositionSizer, DynamicPositionSizer>();
        services.AddScoped<ISignalGenerator, EnhancedSignalGenerator>();

        // Risk and portfolio management
        services.AddScoped<IRiskManager, RiskManager>();
        services.AddScoped<IPortfolioGate, PortfolioGate>();
        services.AddScoped<IStopManager, StopManager>();

        // Market regime detection
        services.AddScoped<IMarketRegimeService, MarketRegimeService>();

        return services;
    }

    /// <summary>
    /// Register machine learning services for signal enhancement
    /// </summary>
    public static IServiceCollection AddMachineLearningServices(this IServiceCollection services)
    {
        // ML Context (singleton for performance)
        services.AddSingleton<Microsoft.ML.MLContext>(provider => new Microsoft.ML.MLContext(seed: 42));

        // ML Features Database Context
        services.AddDbContextFactory<SmaTrendFollower.Data.MLFeaturesDbContext>(options =>
            options.UseSqlite("Data Source=ml_features.db"));

        // ML Services
        services.AddScoped<SmaTrendFollower.MachineLearning.DataPrep.IFeatureExportService,
                          SmaTrendFollower.MachineLearning.DataPrep.FeatureExportService>();
        services.AddSingleton<SmaTrendFollower.MachineLearning.Prediction.ISignalRanker,
                             SmaTrendFollower.MachineLearning.Prediction.SignalRanker>();

        // ML-Enhanced Signal Generator (alternative to standard EnhancedSignalGenerator)
        services.AddScoped<MLEnhancedSignalGenerator>();

        return services;
    }

    /// <summary>
    /// Register production-ready enhanced trading services (volatility, options, notifications, real-time streaming)
    /// </summary>
    public static IServiceCollection AddEnhancedTradingServices(this IServiceCollection services)
    {
        services.AddScoped<IVolatilityManager, VolatilityManager>();
        services.AddScoped<IOptionsStrategyManager, OptionsStrategyManager>();
        services.AddScoped<IDiscordNotificationService, DiscordNotificationService>();

        // Production-ready real-time streaming services
        services.AddScoped<ITickStreamService, TickStreamService>();
        services.AddScoped<IPreMarketFilterService, PreMarketFilterService>();
        services.AddScoped<IBreadthService, BreadthService>();
        services.AddScoped<IExecutionQAService, ExecutionQAService>();

        // Production-ready advanced options analysis
        services.AddScoped<IOptionsFlowAnalysisService, OptionsFlowAnalysisService>();

        // Production-ready enhanced WebSocket client for Polygon Developer subscription
        services.AddScoped<IEnhancedPolygonWebSocketClient, EnhancedPolygonWebSocketClient>();

        // Phase 6: Advanced Filters and Reactive Triggers (Polygon Developer Roadmap)
        services.AddScoped<IVWAPMonitorService, VWAPMonitorService>();
        services.AddScoped<ITickVolatilityGuard, TickVolatilityGuard>();
        services.AddScoped<IRealTimeBreakoutSignal, RealTimeBreakoutSignal>();
        services.AddScoped<IMicrostructurePatternDetector, MicrostructurePatternDetector>();

        // Phase 6: Real-Time Intelligence & Signal Architecture (New Roadmap Services)
        services.AddScoped<IIndexRegimeService, IndexRegimeService>();
        services.AddScoped<IVIXResolverService, VIXResolverService>();
        services.AddScoped<IBreadthMonitorService, BreadthMonitorService>();
        services.AddScoped<IRealTimeExecutionService, RealTimeExecutionService>();

        // Phase 7: Experimental / Strategic Insight Tools (Polygon Developer Roadmap)
        services.AddScoped<ISlippageEstimator, SlippageEstimator>();
        services.AddScoped<ITickBarBuilder, TickBarBuilder>();
        services.AddScoped<ISmartTradeThrottler, SmartTradeThrottler>();

        return services;
    }

    /// <summary>
    /// Register trade execution services with safety wrapper
    /// </summary>
    public static IServiceCollection AddTradeExecutionServices(this IServiceCollection services)
    {
        // Register trade executor with safety wrapper
        services.AddScoped<TradeExecutor>(); // Original executor
        services.AddScoped<ITradeExecutor>(provider =>
        {
            var innerExecutor = provider.GetRequiredService<TradeExecutor>();
            var safetyGuard = provider.GetRequiredService<ITradingSafetyGuard>();
            var logger = provider.GetRequiredService<ILogger<SafeTradeExecutor>>();
            return new SafeTradeExecutor(innerExecutor, safetyGuard, logger);
        });

        return services;
    }

    /// <summary>
    /// Register monitoring and observability services
    /// </summary>
    public static IServiceCollection AddMonitoringServices(this IServiceCollection services)
    {
        // Enhanced performance monitoring
        services.AddSingleton<PerformanceMonitoringService>();
        services.AddSingleton<IEnhancedPerformanceMonitoringService, EnhancedPerformanceMonitoringService>();
        services.AddScoped<AsyncBarFetchingService>();

        // Production-ready monitoring and analytics services
        services.AddSingleton<TradingMetricsService>();
        services.AddSingleton<ITradingMetricsService>(provider => provider.GetRequiredService<TradingMetricsService>());
        services.AddHostedService<TradingMetricsService>(provider => provider.GetRequiredService<TradingMetricsService>());

        services.AddSingleton<SystemHealthService>();
        services.AddSingleton<ISystemHealthService>(provider => provider.GetRequiredService<SystemHealthService>());
        services.AddHostedService<SystemHealthService>(provider => provider.GetRequiredService<SystemHealthService>());

        // Production-ready real-time monitoring services
        services.AddSingleton<RealTimeMarketMonitor>();
        services.AddSingleton<IRealTimeMarketMonitor>(provider => provider.GetRequiredService<RealTimeMarketMonitor>());
        services.AddHostedService<RealTimeMarketMonitor>(provider => provider.GetRequiredService<RealTimeMarketMonitor>());

        services.AddSingleton<LiveSignalIntelligence>();
        services.AddSingleton<ILiveSignalIntelligence>(provider => provider.GetRequiredService<LiveSignalIntelligence>());
        services.AddHostedService<LiveSignalIntelligence>(provider => provider.GetRequiredService<LiveSignalIntelligence>());

        // Production-ready metrics API service
        services.AddSingleton<MetricsApiService>();
        services.AddSingleton<IMetricsApiService>(provider => provider.GetRequiredService<MetricsApiService>());
        services.AddHostedService<MetricsApiService>(provider => provider.GetRequiredService<MetricsApiService>());

        // Trailing stop management - Register as singleton for hosted service
        services.AddSingleton<RealTimeTrailingStopManager>();
        services.AddSingleton<ITrailingStopManager>(provider => provider.GetRequiredService<RealTimeTrailingStopManager>());
        services.AddHostedService<RealTimeTrailingStopManager>(provider => provider.GetRequiredService<RealTimeTrailingStopManager>());

        // State management
        services.AddHostedService<StateFlushService>();

        return services;
    }

    /// <summary>
    /// Register production-ready adaptive optimization services for industrial-grade continuous learning
    /// </summary>
    public static IServiceCollection AddAdaptiveOptimizationServices(this IServiceCollection services)
    {
        // Production-ready adaptive optimization services
        services.AddScoped<IPerformanceAnalysisService, PerformanceAnalysisService>();
        services.AddScoped<IAdaptiveLearningService, AdaptiveLearningService>();
        services.AddScoped<IStrategyOptimizationOrchestrator, StrategyOptimizationOrchestrator>();

        // Production-ready backtesting engine for walk-forward analysis
        services.AddScoped<IBacktestingEngine, BacktestingEngine>();

        return services;
    }

    /// <summary>
    /// Register Quartz.NET scheduling services for automated universe management and VIX training
    /// </summary>
    public static IServiceCollection AddSchedulingServices(this IServiceCollection services)
    {
        // Add Quartz services
        services.AddQuartz(q =>
        {
            q.UseMicrosoftDependencyInjectionJobFactory();

            // Configure the UniverseJobs job
            var jobKey = new JobKey("UniverseJob");
            q.AddJob<SmaTrendFollower.Scheduling.UniverseJobs>(opts => opts.WithIdentity(jobKey));

            // Schedule daily at 8:30 AM ET (12:30 PM UTC)
            q.AddTrigger(t => t
                .ForJob(jobKey)
                .WithIdentity("UniverseTrigger")
                .StartNow()
                .WithCronSchedule("0 30 12 * * ?") // Daily at 12:30 UTC (8:30 AM ET)
                .WithDescription("Daily universe refresh at 8:30 AM ET"));

            // Configure the SyntheticVixTrainer job
            var vixTrainerJobKey = new JobKey("VixTrainer");
            q.AddJob<SyntheticVixTrainer>(opts => opts.WithIdentity(vixTrainerJobKey));

            // Schedule weekly on Sunday at 6:00 PM ET (22:00 UTC)
            q.AddTrigger(t => t
                .ForJob(vixTrainerJobKey)
                .WithIdentity("VixTrainerTrigger")
                .StartNow()
                .WithCronSchedule("0 0 22 ? * SUN") // Weekly on Sunday at 22:00 UTC (6:00 PM ET)
                .WithDescription("Weekly synthetic VIX regression training on Sunday at 6:00 PM ET"));

            // Configure the RedisCleanupService job
            var redisCleanupJobKey = new JobKey("RedisCleanup");
            q.AddJob<RedisCleanupService>(opts => opts.WithIdentity(redisCleanupJobKey));

            // Schedule daily at 2:00 AM ET (6:00 AM UTC)
            q.AddTrigger(t => t
                .ForJob(redisCleanupJobKey)
                .WithIdentity("RedisCleanupTrigger")
                .StartNow()
                .WithCronSchedule("0 0 6 * * ?") // Daily at 6:00 AM UTC (2:00 AM ET)
                .WithDescription("Daily Redis key hygiene cleanup at 2:00 AM ET"));

            // Configure the ML Model Retrainer job
            var mlRetrainerJobKey = new JobKey("MLModelRetrainer");
            q.AddJob<SmaTrendFollower.Scheduling.MLModelRetrainerJob>(opts => opts.WithIdentity(mlRetrainerJobKey));

            // Schedule weekly on Sunday at 2:00 AM ET (6:00 AM UTC)
            q.AddTrigger(t => t
                .ForJob(mlRetrainerJobKey)
                .WithIdentity("MLRetrainerTrigger")
                .StartNow()
                .WithCronSchedule("0 0 6 ? * SUN") // Weekly on Sunday at 6:00 AM UTC (2:00 AM ET)
                .WithDescription("Weekly ML model retraining on Sunday at 2:00 AM ET"));
        });

        // Add Quartz hosted service
        services.AddQuartzHostedService(opt =>
        {
            opt.WaitForJobsToComplete = true;
            opt.AwaitApplicationStarted = true;
        });

        return services;
    }



    /// <summary>
    /// Register backtesting and replay services
    /// </summary>
    public static IServiceCollection AddBacktestingServices(this IServiceCollection services)
    {
        services.AddSingleton<SmaTrendFollower.Backtesting.Replay.PolygonTickLoader>();
        services.AddSingleton<SmaTrendFollower.Backtesting.Replay.IVirtualTimeProvider, SmaTrendFollower.Backtesting.Replay.VirtualTimeProvider>();
        services.AddSingleton<SmaTrendFollower.Backtesting.Replay.BacktestReplayEngine>();
        services.AddControllers(); // For web API dashboard
        return services;
    }

    /// <summary>
    /// Register all services for 100% production-ready trading system
    /// </summary>
    public static IServiceCollection AddFullTradingSystem(this IServiceCollection services, IConfiguration configuration)
    {
        return services
            .AddPollyHttpClients(configuration) // 🚀 Polly resilience policies first
            .AddCoreInfrastructure()
            .AddDataServices()
            .AddMarketDataServices()
            .AddSafetyServices()
            .AddTradingServices()
            .AddMachineLearningServices() // 🚀 ML-enhanced signal generation
            .AddEnhancedTradingServices()
            .AddTradeExecutionServices()
            .AddMonitoringServices()
            .AddAdaptiveOptimizationServices() // 🚀 Production-ready adaptive optimization
            .AddSchedulingServices() // 🚀 Automated universe management
            .AddBacktestingServices() // 🚀 Backtesting and replay functionality
            .AddObservabilityServices(configuration); // 🚀 Prometheus metrics and health checks
    }

    /// <summary>
    /// Register observability services including Prometheus metrics and health checks
    /// </summary>
    public static IServiceCollection AddObservabilityServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Check if health checks are already registered
        var healthCheckServiceDescriptor = services.FirstOrDefault(s => s.ServiceType == typeof(Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckService));

        if (healthCheckServiceDescriptor == null)
        {
            // Add health checks only if not already registered
            var healthChecksBuilder = services.AddHealthChecks()
                .AddCheck("self", () => Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy("Application is running"));

            // Add Redis health check if Redis is configured
            var redisConnectionString = configuration.GetConnectionString("Redis") ?? "*************:6379";
            if (!string.IsNullOrEmpty(redisConnectionString))
            {
                healthChecksBuilder.AddRedis(redisConnectionString, name: "redis", tags: new[] { "cache", "infrastructure" });
            }
        }

        return services;
    }

    /// <summary>
    /// Register minimal services for testing/examples
    /// </summary>
    public static IServiceCollection AddMinimalServices(this IServiceCollection services, IConfiguration configuration)
    {
        return services
            .AddPollyHttpClients(configuration)
            .AddCoreInfrastructure()
            .AddDataServices()
            .AddMarketDataServices();
    }

    /// <summary>
    /// Register services for cache operations only
    /// </summary>
    public static IServiceCollection AddCacheServices(this IServiceCollection services)
    {
        services.AddHttpClient();
        services.AddSingleton<IRateLimitPolicyFactory, RateLimitPolicyFactory>();

        // Database contexts
        services.AddDbContext<IndexCacheDbContext>(options =>
            options.UseSqlite("Data Source=index_cache.db"));
        services.AddDbContext<StockBarCacheDbContext>(options =>
            options.UseSqlite("Data Source=stock_cache.db"));

        // Cache services
        services.AddScoped<IIndexCacheService, IndexCacheService>();
        services.AddScoped<IStockBarCacheService, StockBarCacheService>();
        services.AddScoped<ICacheMaintenanceService, CacheMaintenanceService>();

        // Database initialization service
        services.AddScoped<IDatabaseInitializationService, DatabaseInitializationService>();

        return services;
    }

    /// <summary>
    /// Register services for signal generation testing
    /// </summary>
    public static IServiceCollection AddSignalTestingServices(this IServiceCollection services, IConfiguration configuration)
    {
        return services
            .AddMinimalServices(configuration)
            .AddTradingServices();
    }

    /// <summary>
    /// Register the trading service implementation (always uses enhanced version)
    /// </summary>
    public static IServiceCollection AddTradingServiceImplementation(this IServiceCollection services, bool useEnhanced = true)
    {
        // Always use enhanced trading service since basic version was removed in Phase 2 simplification
        services.AddScoped<ITradingService, EnhancedTradingService>();
        return services;
    }

    /// <summary>
    /// Initializes all databases for the service provider
    /// </summary>
    public static async Task InitializeDatabasesAsync(this IServiceProvider serviceProvider)
    {
        using var scope = serviceProvider.CreateScope();
        var dbInitService = scope.ServiceProvider.GetService<IDatabaseInitializationService>();

        if (dbInitService != null)
        {
            await dbInitService.InitializeAllDatabasesAsync();
        }
    }
}
