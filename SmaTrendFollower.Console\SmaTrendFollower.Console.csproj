﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <AssemblyName>SmaTrendFollower.Console</AssemblyName>
    <RootNamespace>SmaTrendFollower</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

<ItemGroup>
  <!-- Alpaca SDK -->
  <PackageReference Include="Alpaca.Markets" Version="7.2.0" />
  <PackageReference Include="AspNetCore.HealthChecks.Redis" Version="9.0.0" />
  <PackageReference Include="CsvHelper" Version="33.1.0" />
  <PackageReference Include="MathNet.Numerics" Version="5.0.0" />
  <PackageReference Include="Microsoft.CodeAnalysis.Common" Version="4.9.2" />
  <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.6">
    <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    <PrivateAssets>all</PrivateAssets>
  </PackageReference>

  <!-- Microsoft hosting / DI -->
  <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.6" />
  <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.6" />
  <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.6" />
  <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="9.0.6" />

  <!-- Entity Framework / SQLite -->
  <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.6" />
  <PackageReference Include="Microsoft.ML" Version="4.0.2" />
  <PackageReference Include="Microsoft.ML.AutoML" Version="0.22.2" />
  <PackageReference Include="prometheus-net.AspNetCore" Version="8.2.1" />
  <PackageReference Include="Quartz" Version="3.14.0" />
  <PackageReference Include="Quartz.Extensions.Hosting" Version="3.14.0" />
  <PackageReference Include="ScottPlot" Version="5.0.55" />

  <!-- Serilog -->
  <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
  <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
  <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />  <!-- NEW -->

  <!-- Redis -->
  <PackageReference Include="StackExchange.Redis" Version="2.8.41" />

  <!-- Performance Monitoring -->
  <PackageReference Include="System.Diagnostics.PerformanceCounter" Version="8.0.0" />

  <!-- Utilities -->
  <PackageReference Include="DotNetEnv" Version="2.4.0" />
  <PackageReference Include="Skender.Stock.Indicators" Version="2.6.1" />
  <PackageReference Include="System.Text.Json" Version="9.0.6" />
  <PackageReference Include="TimeZoneConverter" Version="3.3.0" />
  <PackageReference Include="System.IO.Compression" Version="4.3.0" />
</ItemGroup>


</Project>
